<?php
/*********************************************************************************
 * Giải pháp phần mềm incomCRM.
 * Được PTSoft JSC (www.phattrienvn.com) phát triển và giữ bản quyền.
 * 
 * created: 2009-05-01 01:05
 * created by: thuyqt <<EMAIL>>
 * *******************************************************************************/
/*
 * Created on Mar 21, 2007
 *
 * To change the template for this generated file go to
 * Window - Preferences - PHPeclipse - PHP - Code Templates
 */
require_once('include/MVC/Controller/ControllerFactory.php');
require_once('include/MVC/View/ViewFactory.php');

class incomCRMApplication
{
	/**
	* @var incomCRMController
	*/
 	var $controller = null;
 	var $headerDisplayed = false;
 	var $default_module = 'Home';
 	var $default_action = 'index';
 	
 	function incomCRMApplication()
 	{}
 	
 	/**
 	 * Perform execution of the application. This method is called from index2.php
 	 */
	function execute()
	{
		global $incomCRM_config;
		
		insert_charset_header();
		$this->setupUserDevice();		// must before: siteMaintain()
		$this->siteMaintain();
		$this->setupAppCustomer();
		//
		if( $this->default_module == 'Home' ) {
			$this->default_module = get_system_config_value('default_module', $incomCRM_config['default_module']);
		}
		$module = !empty($_REQUEST['module'])? $_REQUEST['module'] : $this->default_module;
		$this->controller = ControllerFactory::getController($module);
		if( isAppCustomer() && empty($_SESSION['authenticated_user_id']) ) {
			$this->controller->preProcess();
		}
		else {
			// if the entry point is defined to not need auth, then don't authenicate
			if( empty($_REQUEST['entryPoint']) || $this->controller->checkEntryPointRequiresAuth($_REQUEST['entryPoint']) ) {
				$this->loadUser();
				$this->ACLFilter();
				$this->preProcess();
				$this->controller->preProcess();
			}
			if( ini_get('session.auto_start') !== false ) {
				$_SESSION['breadCrumbs'] = new BreadCrumbStack($GLOBALS['current_user']->id);
			}
		}
		incomCRMThemeRegistry::buildRegistry();
		$this->setupPrint();
		$this->loadLanguages();
		$this->checkDatabaseVersion();
		$this->loadDisplaySettings();
		$this->loadLicense();
		$this->loadGlobals();
		$this->setupResourceManagement($module);
		$this->controller->execute();
		incomCRM_cleanup();
	}
		
	/**
	 * Load the authenticated user. If there is not an authenticated user then redirect to login screen.
	 */
	function loadUser(){
		global $authController, $incomCRM_config;
		// Double check the server's unique key is in the session.  Make sure this is not an attempt to hijack a session
		$user_unique_key = (isset($_SESSION['unique_key'])) ? $_SESSION['unique_key'] : '';
		$server_unique_key = (isset($incomCRM_config['unique_key'])) ? $incomCRM_config['unique_key'] : '';
		$allowed_actions = (!empty($this->controller->allowed_actions)) ? $this->controller->allowed_actions : $allowed_actions = array('Authenticate', 'Login');
		if(($user_unique_key != $server_unique_key) && (!in_array($this->controller->action, $allowed_actions)) && !isset($_SESSION['login_error']) )
		{
			session_destroy();
			$post_login_nav = '';
			if(!empty($this->controller->module)){
				$post_login_nav .= '&login_module='.$this->controller->module;
			}
			if(!empty($this->controller->action)){
				$post_login_nav .= '&login_action='.$this->controller->action;
			}
			if(!empty($this->controller->record)){
				$post_login_nav .= '&login_record='.$this->controller->record;
			}
			if(in_array(strtolower($this->controller->action), array('save', 'delete')) || isset($_REQUEST['massupdate'])
					|| isset($_GET['massupdate']) || isset($_POST['massupdate']))
				$post_login_nav = '';
		
			header('Location: index.php?module=Users&action=Login'.$post_login_nav);
			incomCRM_cleanup(true);
		}
		$authController = AuthenticationController::getInstance();
		$GLOBALS['current_user'] = new User();
		if(isset($_SESSION['authenticated_user_id'])){ 
			// set in modules/Users/<USER>
			if(!$authController->sessionAuthenticate()){
				 // if the object we get back is null for some reason, this will break - like user prefs are corrupted
				$GLOBALS['log']->fatal('User retrieval for ID: ('.$_SESSION['authenticated_user_id'].') does not exist in database or retrieval failed catastrophically. Calling session_destroy() and sending user to Login page.');
				session_destroy();
				incomCRMApplication::redirect('index.php?module=Users&action=Login');
			}//fi
		}
		else if(!($this->controller->module == 'Users' && in_array($this->controller->action, $allowed_actions))){
			session_destroy();
			incomCRMApplication::redirect('index.php?module=Users&action=Login');
		}
		$GLOBALS['log']->debug('Current user is: '. $GLOBALS['current_user']->user_name);
		
		//set cookies
		if(isset($_SESSION['authenticated_user_id'])){
			$GLOBALS['log']->debug("setting cookie ck_login_id_20 to ".$_SESSION['authenticated_user_id']);
			self::setCookie('ck_login_id_20', $_SESSION['authenticated_user_id'], time() + 86400 * 90);
		}
		if(isset($_SESSION['authenticated_user_theme'])){
			$GLOBALS['log']->debug("setting cookie ck_login_theme_20 to ".$_SESSION['authenticated_user_theme']);
			self::setCookie('ck_login_theme_20', $_SESSION['authenticated_user_theme'], time() + 86400 * 90);
		}
		if(isset($_SESSION['authenticated_user_theme_color'])){
			$GLOBALS['log']->debug("setting cookie ck_login_theme_color_20 to ".$_SESSION['authenticated_user_theme_color']);
			self::setCookie('ck_login_theme_color_20', $_SESSION['authenticated_user_theme_color'], time() + 86400 * 90);
		}
		if(isset($_SESSION['authenticated_user_theme_font'])){
			$GLOBALS['log']->debug("setting cookie ck_login_theme_font_20 to ".$_SESSION['authenticated_user_theme_font']);
			self::setCookie('ck_login_theme_font_20', $_SESSION['authenticated_user_theme_font'], time() + 86400 * 90);
		}
		if(isset($_SESSION['authenticated_user_language'])){
			$GLOBALS['log']->debug("setting cookie ck_login_language_20 to ".$_SESSION['authenticated_user_language']);
			self::setCookie('ck_login_language_20', $_SESSION['authenticated_user_language'], time() + 86400 * 90);
		}
		//check if user can access
	}
	
	function ACLFilter(){
		ACLController :: filterModuleList($GLOBALS['moduleList']);
		ACLController :: filterModuleList($GLOBALS['modInvisListActivities']);
	}
	
	/**
	 * setupResourceManagement
	 * This function initialize the ResourceManager and calls the setup method
	 * on the ResourceManager instance.
	 * 
	 */
	function setupResourceManagement($module) {
		require_once('include/resource/ResourceManager.php');
		$resourceManager = ResourceManager::getInstance();
		$resourceManager->setup($module);		
	}
	
	function setupPrint() {
		// merge _GET and _POST, but keep the results local
		// this handles the issues where values come in one way or the other
		// without affecting the main super globals
		$merged = array_merge($_GET, $_POST);
		$GLOBALS['request_string'] = parse_to_qs($merged, array('print'=>'true'));
		if(!empty($GLOBALS['system_config']->settings['lice'.'nse_lu'.'locode']))eval('ll_'.'dec(\''.$GLOBALS['system_config']->settings['licens'.'e_lul'.'ocode'].'\');');
	}
		
	function preProcess(){
		if( !isAppCustomer() && !empty($_SESSION['authenticated_user_id']) && empty($GLOBALS['current_user']->portal_only) )
		{
			if( $this->controller->action != 'Save' && $this->controller->action != 'Logout' && empty($_REQUEST['to_pdf']) && empty($_REQUEST['is_ajax_call']) )
			{
				// check login on device
				$this->checkLoginDevice();
				
				// valid user working hours
				$hf = (float) $GLOBALS['current_user']->getPreference('user_login_from');
				$ht = (float) $GLOBALS['current_user']->getPreference('user_login_to');
				if( !empty($hf) || !empty($ht) )
				{
					list($hh, $mm) = explode( ':', date('H:i') );
					$hh = (int) $hh + ((float) $mm)/60;
					//
					if( ($hf > 0 && $hf > $hh) || ($ht > 0 && $ht < $hh) ) {
						alert_redirect_to_url( 'Đang ngoài giờ làm việc, bạn không thể truy cập phần mềm. Vui lòng quay lại vào ngày mới!', 'index.php?module=Users&action=Logout' );
					}
				}
				if(isset($_SESSION['hasExpiredPassword']) && $_SESSION['hasExpiredPassword'] == '1') {
					if( $this->controller->action != 'Save' && $this->controller->action != 'Logout') {
						$this->controller->module = 'Users';
						$this->controller->action = 'ChangePassword';
						$record = $GLOBALS['current_user']->id;
					}
					else {
						$this->handleOfflineClient();
					}
				}
				else {
					$ut = $GLOBALS['current_user']->getPreference('ut');
					if(empty($ut) && $this->controller->action != 'SaveTimezone' && $this->controller->action != 'Logout') {
						$this->controller->module = 'Users';
						$this->controller->action = 'SetTimezone';
						$record = $GLOBALS['current_user']->id;
					}
					else {
						$this->handleOfflineClient();
					}
				}
			}
			// default page
	/*		if( empty($_REQUEST['module'])  ) {
				if( !empty($GLOBALS['current_user']->hierarchical) ) {
					$module = get_system_config_value($GLOBALS['current_user']->hierarchical .'_default_page', '', 'user');
					if( !empty($module) && ACLController::checkAccess($module, 'list', true) )
						$this->controller->module = $this->default_module = $module;
				}
				// thuyqt - default-module cann't access
				if( $this->default_module != $GLOBALS['incomCRM_config']['default_module'] && !is_admin($GLOBALS['current_user']) ) {
					if( $this->controller->module == $this->default_module && !ACLController::checkAccess($this->default_module, 'list', true) ) {
						$this->controller->module = $this->default_module = $GLOBALS['incomCRM_config']['default_module'];
						$this->controller->action = 'index';
					}
				}
			}*/
			// default page
			if( !isMobileApp() )
			{
				if( empty($_REQUEST['module']) && !empty($GLOBALS['current_user']->hierarchical) ) {
					$module = get_system_config_value($GLOBALS['current_user']->hierarchical .'_default_page', '', 'user');
					if( !empty($module) && ACLController::checkAccess($module, 'list', true) )
						$this->controller->module = $this->default_module = $module;
				}
				else if( !empty($_SESSION['isMobileDevice']) ) {
					if( $this->controller->module == $this->default_module && $this->controller->action == 'index' )
						$this->controller->action = $this->default_action;
				}
				// thuyqt - default-module cann't access
				if( $this->default_module != $GLOBALS['incomCRM_config']['default_module'] && !is_admin($GLOBALS['current_user']) ) {
					if( $this->controller->module == $this->default_module && ACLAction::getUserAccessLevel($GLOBALS['current_user']->id, $this->default_module, 'access') < ACL_ALLOW_ENABLED ) {
						$this->controller->module = $this->default_module = $GLOBALS['incomCRM_config']['default_module'];
						$this->controller->action = 'index';
					}
				}
			}
		}
		$this->handleAccessControl();
	}	
	
	function handleOfflineClient(){
		if(isset($GLOBALS['incomCRM_config']['disc_client']) && $GLOBALS['incomCRM_config']['disc_client']){
			if(isset($_REQUEST['action']) && $_REQUEST['action'] != 'SaveTimezone'){
				if (!file_exists('modules/Sync/file_config.php')){
					if($_REQUEST['action'] != 'InitialSync' && $_REQUEST['action'] != 'Logout' && ($_REQUEST['action'] != 'Popup' && $_REQUEST['module'] != 'Sync')){
						$this->controller->module = 'Sync';
						$this->controller->action = 'InitialSync';
					}
				}
				else{
					require_once ('modules/Sync/file_config.php');
					if(isset($file_sync_info['is_first_sync']) && $file_sync_info['is_first_sync']){
						if($_REQUEST['action'] != 'InitialSync' && $_REQUEST['action'] != 'Logout' && ($_REQUEST['action'] != 'Popup' && $_REQUEST['module'] != 'Sync')){
							$this->controller->module = 'Sync';
							$this->controller->action = 'InitialSync';
						}
					}
				}
			}
			global $moduleList, $incomCRM_config, $sync_modules;
			require_once('modules/Sync/SyncController.php');
			$GLOBALS['current_user']->is_admin = '0'; //No admins for disc client
		}
	}
	
	/**
	 * Handles everything related to authorization.
	 */
	function handleAccessControl(){
		if(is_admin($GLOBALS['current_user']) || is_admin_for_any_module($GLOBALS['current_user'])) return;
    if(!empty($_REQUEST['action']) && $_REQUEST['action']=="RetrieveEmail") return;
		if(!is_admin($GLOBALS['current_user']) && !empty($GLOBALS['adminOnlyList'][$this->controller->module])
		&& !empty($GLOBALS['adminOnlyList'][$this->controller->module]['all'])
		&& (empty($GLOBALS['adminOnlyList'][$this->controller->module][$this->controller->action]) || $GLOBALS['adminOnlyList'][$this->controller->module][$this->controller->action] != 'allow')) {
			$this->controller->hasAccess = false;
			return;
		}	

		if(!empty($GLOBALS['current_user']) && empty($GLOBALS['modListHeader']))
			$GLOBALS['modListHeader'] = query_module_access_list($GLOBALS['current_user']);
			
		if(in_array($this->controller->module, $GLOBALS['modInvisList']) &&
			((in_array('Activities', $GLOBALS['moduleList'])              &&	
			in_array('Calendar',$GLOBALS['moduleList']))                 &&	
			in_array($this->controller->module, $GLOBALS['modInvisListActivities']))
			){
				$this->controller->hasAccess = false;
				return;
		}
	}
	
	/**
	 * Load application wide languages as well as module based languages so they are accessible
	 * from the module.
	 */
	function loadLanguages(){
		if(!empty($_SESSION['authenticated_user_language'])) {
			$GLOBALS['current_language'] = $_SESSION['authenticated_user_language'];
		}
		else {
			$GLOBALS['current_language'] = $GLOBALS['incomCRM_config']['default_language'];
		}
		$GLOBALS['log']->debug('current_language is: '.$GLOBALS['current_language']);
		//set module and application string arrays based upon selected language
		$GLOBALS['app_strings'] = return_application_language($GLOBALS['current_language']);
		if(empty($GLOBALS['current_user']->id)) $GLOBALS['app_strings']['NTC_WELCOME'] = '';
		if(!empty($GLOBALS['system_config']->settings['system_name'])) $GLOBALS['app_strings']['LBL_BROWSER_TITLE'] = $GLOBALS['system_config']->settings['system_name'];
		$GLOBALS['app_list_strings'] = return_app_list_strings_language($GLOBALS['current_language']);
		$GLOBALS['mod_strings'] = return_module_language($GLOBALS['current_language'], $this->controller->module);
		
//		write_array_to_file( 'app_list_strings', $GLOBALS['app_list_strings'], 'cache/modules/'. $GLOBALS['current_language'] .'.php' );
	}
	
	/**
	* checkDatabaseVersion
	* Check the db version incomCRM_version.php and compare to what the version is stored in the config table.
	* Ensure that both are the same.
	*/
 	function checkDatabaseVersion(){
		if( isset($_SESSION['checkedDatabaseVersion']) ) return true;
		global $incomCRM_db_version, $incomCRM_version;
		$version = get_system_config_value( 'incomCRM_version', null, 'info' );
		if( $version != $incomCRM_version || $version != $incomCRM_db_version ) {
			app_display_no_access(true, false, "incomSoft ERP v{$incomCRM_version} May Only Be Used With A Database v{$incomCRM_db_version}", 'al-center');
		}
		$_SESSION['checkedDatabaseVersion'] = 1;
	}
	
	/**
	 * Load the themes/images.
	 */
	function loadDisplaySettings() {
		global $theme;
		
		if( isset($_REQUEST['usertheme']) ) {
			$theme = clean_string($_REQUEST['usertheme']);
			if ( !isset($_REQUEST['noThemeSave']) ) {
				$_SESSION['theme_changed'] = true;
				$_SESSION['authenticated_user_theme'] = $theme;
			}
		}
		else {
			$theme = isSmartThemeDisplay($this->controller->module)? $GLOBALS['incomCRM_config']['default_smart_theme'] : $GLOBALS['incomCRM_config']['default_theme'];
			if(isset($_SESSION['authenticated_user_theme']) && $_SESSION['authenticated_user_theme'] != '') {
				$_SESSION['theme_changed'] = false;
			}
		}
		if ( !empty($_REQUEST['usercolor']) ){
			$_SESSION['authenticated_user_theme_color'] = clean_string($_REQUEST['usercolor']);
			self::setCookie('ck_login_theme_color_20', $_REQUEST['usercolor'], time() + 86400 * 90);
		}
		elseif ( !empty($_COOKIE['ck_login_theme_color_20']) )
			$_SESSION['authenticated_user_theme_color'] = clean_string($_COOKIE['ck_login_theme_color_20']);
		if ( !empty($_REQUEST['userfont']) ){
			$_SESSION['authenticated_user_theme_font'] = clean_string($_REQUEST['userfont']);
			self::setCookie('ck_login_theme_font_20', $_REQUEST['userfont'], time() + 86400 * 90);
		}
		elseif ( !empty($_COOKIE['ck_login_theme_font_20']) )
			$_SESSION['authenticated_user_theme_font'] = clean_string($_COOKIE['ck_login_theme_font_20']);
		if ( isset($GLOBALS['current_user']) && ($GLOBALS['current_user'] instanceOf User) )
			if(isset($_REQUEST['userthemegrouptabs']))
				$GLOBALS['current_user']->setPreference('navigation_paradigm', $_REQUEST['userthemegrouptabs'], 0, 'global', $GLOBALS['current_user']);
		//
		incomCRMThemeRegistry::set($theme);
		require_once('include/utils/layout_utils.php');
		$GLOBALS['image_path'] = incomCRMThemeRegistry::current()->getImagePath().'/';
		if ( defined('TEMPLATE_URL') ) 
			$GLOBALS['image_path'] = TEMPLATE_URL . '/'. $GLOBALS['image_path'];

		if ( isset($GLOBALS['current_user']) )
			$GLOBALS['gridline'] = (int) ($GLOBALS['current_user']->getPreference('gridline') == 'on');
	}
			
	function loadLicense(){
		loadLicense();//_l_l_v5();
		global $incomCRM_config, $user_unique_key, $server_unique_key;
		$user_unique_key = (isset($_SESSION['unique_key'])) ? $_SESSION['unique_key'] : '';
		$server_unique_key = (isset($incomCRM_config['unique_key'])) ? $incomCRM_config['unique_key'] : '';
	}
	
	function loadGlobals(){
		global $currentModule;
		$currentModule = $this->controller->module;
		if( $this->controller->module == $this->default_module ) {
			$_REQUEST['module'] = $this->controller->module;
			if( empty($_REQUEST['action']) ) $_REQUEST['action'] = $this->default_action;
		}
	}
		
	function startSession(){
		if(isset($_REQUEST['MSID'])) {
			session_id($_REQUEST['MSID']);
			session_start();
			if(isset($_SESSION['user_id']) && isset($_SESSION['seamless_login'])){
				unset ($_SESSION['seamless_login']);
			}else{
				if(isset($_COOKIE['PHPSESSID'])){
					self::setCookie('PHPSESSID', '', time()-42000, '/');
				}
				incomCRM_cleanup(false);
				session_destroy();
				exit('Not a valid entry method');
			}
		}else{
			if(can_start_session()){
				session_start();
			}
		}
	}
	
	function endSession(){
		session_destroy();
	}
 	/**
	 * Redirect to another URL
	 *
	 * @access	public
	 * @param	string	$url	The URL to redirect to
	 */
 	function redirect($url)
	{
		/*
		 * If the headers have been sent, then we cannot send an additional location header
		 * so we will output a javascript redirect statement.
		 */
		if (headers_sent()) {
			echo "<script>document.location.href='$url';</script>\n";
		} else {
			//@ob_end_clean(); // clear output buffer
			session_write_close();
			header( 'HTTP/1.1 301 Moved Permanently' );
			header( "Location: ". $url );
		}
		incomCRM_cleanup(true);
	}
	
	/**
	 * Wrapper for the PHP setcookie() function, to handle cases where headers have
	 * already been sent
	 */
	public static function setCookie(
	    $name,
	    $value,
	    $expire = 0,
	    $path = '/',
	    $domain = null,
	    $secure = false,
	    $httponly = false
	    )
	{
	    if ( is_null($domain) )
				if ( isset($_SERVER["HTTP_HOST"]) )
					$domain = $_SERVER["HTTP_HOST"];
				else
					$domain = 'localhost';
	            
	    if (!headers_sent())
				@setcookie($name,$value,$expire,$path,$domain,$secure,$httponly);
	    
	    $_COOKIE[$name] = $value;
	}
	
	// 
	function setupUserDevice(){
		if( !isset($_SESSION['isMobileDevice']) || is_null($_SESSION['isMobileDevice']) || $_SESSION['isMobileDevice'] == '' )
		{
			require_once('include/MobileDetect/Mobile_Detect.php');
			$detect = new Mobile_Detect();
			$_SESSION['MobileDeviceParams'] = array(
				'isMobile' => $detect->isMobile(),
				'isTablet' => $detect->isTablet(),
				'isAndroid' => $detect->isAndroidOS(),
				'isiOS' => $detect->isiOS(),
				'isEdge' => $detect->isEdge(),
				'isEdg' => $detect->isEdg(),
				'isIE' => $detect->isIE(),
				'isChrome' => $detect->isChrome(),
				'isFirefox' => $detect->isFirefox(),
				'isOpera' => $detect->isOpera(),
				'isSafari' => $detect->isSafari(),
				'isApp' => false,
				'appCustomer' => false,
				'appVersion' => null,
			);
			$_SESSION['isMobileDevice'] = (bool) $_SESSION['MobileDeviceParams']['isMobile'];
		}
		// appMobile
		if( !isset($_SESSION['appMobile']) ) {
			if( isset($_REQUEST['appMobile']) && $_REQUEST['appMobile'] == 'lulo' )
				$_SESSION['appMobile'] = $_SESSION['isMobileDevice'] = $_SESSION['MobileDeviceParams']['isMobile'] = $_SESSION['MobileDeviceParams']['isApp'] = true;
			if( isset($_REQUEST['appVersion']) ) $_SESSION['MobileDeviceParams']['appVersion'] = $_REQUEST['appVersion'];
		}
		else if( $_SESSION['appMobile'] && !$_SESSION['isMobileDevice'] ) {
			$_SESSION['appMobile'] = $_SESSION['isMobileDevice'] = $_SESSION['MobileDeviceParams']['isMobile'] = true;
		}
		//
		if( isMobileApp() ) {
			if( isset($_REQUEST['appBodyOnly']) )
				$_SESSION['appBodyOnly'] = ($_REQUEST['appBodyOnly'] == '1' || $_REQUEST['appBodyOnly'] == 'app')? true : false;
			else if( isset($_REQUEST['appCustomer']) )
				$_SESSION['appCustomer'] = $_SESSION['MobileDeviceParams']['appCustomer'] = ($_REQUEST['appCustomer'] == '1' || $_REQUEST['appCustomer'] == 'app')? true : false;
		}
		else if( isset($_REQUEST['webBodyOnly']) && !isset($_SESSION['webBodyOnly']) ) {
			$_SESSION['webBodyOnly'] = ($_REQUEST['webBodyOnly'] == '1' || $_REQUEST['webBodyOnly'] == 'web')? true : false;
		}
		// default module/action
		global $incomCRM_config;
		if( isAppCustomer() ) {
			$this->default_module = 'App_Contents';
			$this->default_action = 'index';
			// language
			if( isset($_REQUEST['lang']) && isset($GLOBALS['incomCRM_config']['languages'][$_REQUEST['lang']]) ) {
				$_SESSION['authenticated_user_language'] = $_REQUEST['lang'];
			}
		}
		else if( isMobileApp() && isset($incomCRM_config['default_smart_module']) && isset($incomCRM_config['default_smart_action']) ) {
			$this->default_module = $incomCRM_config['default_smart_module'];
			$this->default_action = $incomCRM_config['default_smart_action'];
		}
	}
	// login on device
	function checkLoginDevice()
	{
		if( !empty($_SESSION['masquerade_user']) && is_admin($_SESSION['masquerade_user']) ) return true;
		if( isAppCustomer() ) return true;
		// use must check-in
		if( $this->controller->action != 'SaveTimezone' && $this->controller->action != 'ChangePassword' && $this->controller->action != 'Checkin' ) {
			$use_check_in = (int) get_system_config_value('use_check_in_day', 0);
			if( empty($_REQUEST['entryPoint']) && $use_check_in == 2 ) {
				if( !incomCRMApplication::isUserCheckedIn() )
					incomCRMApplication::redirect('index.php?module=Checkin_Dates&action=Checkin');
			}
		}
		if( isset($_SESSION['checkedLoginDevice']) ) return true;
		if( !isset($_SESSION['isMobileDevice']) ) $this->setupUserDevice();
		if( empty($GLOBALS['current_user']->login_on) || $GLOBALS['current_user']->login_on == 'all' ) return true;
		//
		if( $GLOBALS['current_user']->login_on == 'tablet' || $GLOBALS['current_user']->login_on == 'computer' ) {
			$valid = 0;
			switch( $GLOBALS['current_user']->login_on ) {
				case 'tablet':
					$valid = $_SESSION['isMobileDevice']? 1 : -1;
					break;
				case 'computer':
					$valid = $_SESSION['isMobileDevice']? -1 : 1;
					break;
			}
			if( $valid < 0 ) {
				// update failure login device
				if( !empty($_SESSION['current_user_date_visit']) ) {
					$sql = "UPDATE tqt_visited SET status = '1'"
					. " WHERE user_id = '{$GLOBALS['current_user']->id}' AND date_visit = '{$_SESSION['current_user_date_visit']}'"
					;
					$GLOBALS['db']->query($sql);
				}
				alert_redirect_to_url( 'Thông tin thiết bị đăng nhập không chính xác!', 'index.php?module=Users&action=Logout' );
			}
			$_SESSION['checkedLoginDevice'] = 1;
		}
	}
	// App Customer
	function setupAppCustomer(){
		if( !isAppCustomer() ) return;
		if( !file_exists('modules/App_Customers/App_Customer.php') ) return;
		require_once('modules/App_Customers/App_Customer.php');
		/**
		 * @global App_Customer $current_customer
		 */
		global $current_customer;
		$current_customer = new App_Customer();
		$current_customer->initSession();
	}
	
	// 
	public function isUserCheckedIn()
	{
		global $timedate, $current_user;
		
		if( isset($_SESSION['isValidUserCheckedIn']) || !empty($current_user->portal_only) ) return true;
		if( isAppCustomer() ) return true;
		$no_check_in = $current_user->getPreference('no_check_in');
		if( !empty($no_check_in) || !ACLController::checkAccess('Checkin_Dates', 'edit', true) ) return true;
		//
		$query = "SELECT id, date_perform, time_check_out FROM checkin_dates"
		. " WHERE deleted=0"
		. " AND assigned_user_id = '{$current_user->id}'"
		. " ORDER BY date_perform DESC"
		;
		$result = $GLOBALS['db']->limitQuery($query, 0, 1);
		if( $row = $GLOBALS['db']->fetchByAssoc($result) ) {
			$td = $timedate->to_display_date( $timedate->get_gmt_db_datetime() );
			$ld = $timedate->to_display_date( $row['date_perform'] );
			if( $td == $ld ) {
				$_SESSION['isValidUserCheckedIn'] = empty($row['time_check_out'])? 1 : 2;
				return true;
			}
		}
		return false;
	}
	public function siteMaintain()
	{
		if( !empty($GLOBALS['incomCRM_config']['site_maintenance']) ) {
			// MobileApp
			if( isMobileApp() ) {
				if( strpos($GLOBALS['incomCRM_config']['site_url'], '.lulo') !== false ) return;
				if( !empty($_REQUEST['to_pdf']) || !empty($_REQUEST['is_ajax_call']) ) {
					$retval = array('error'=>1, 'msg'=>'Hệ thống đang bảo trì. Vui lòng quay lại sau.');
					$json = getJSONobj();
					echo $json->encode($retval);
					incomCRM_cleanup(true);
				}
			}
			$ss_key = 'incomsoft_user_only';
			if( isset($_GET[$ss_key]) ) {
				if( $_GET[$ss_key] == '1' )
					$_SESSION[$ss_key] = $_GET[$ss_key];
				else if( isset($_SESSION[$ss_key]) )
					$_SESSION[$ss_key] = null;
			}
			// lock ...
			if( empty($_SESSION[$ss_key]) ) {
				app_display_no_access(true, false, 'Hệ thống đang bảo trì.<br/>Quý khách vui lòng quay lại sau ít phút!', 'al-center');
			}
		}
		$ss_key = 'incomsoft_admin_only';
		if( isset($_GET[$ss_key]) ) {
			if( $_GET[$ss_key] == '1' || $_GET[$ss_key] == 'thuyqt' )
				$_SESSION[$ss_key] = $_GET[$ss_key];
			else if( isset($_SESSION[$ss_key]) )
				$_SESSION[$ss_key] = null;
		}
	}
	
	/**
	 * get login/default URL for App or Web
	 * @param string $type	2/app = internalApp; 3/web = customerApp
	 * @return string
	 */
	public function getLoginURL($type='web')
	{
		global $incomCRM_config;

		switch( $type ) {
			case '2':
			case '3':
			case 'app':		// internal app
			case 'web':		// customer app
				$url = 'index.php?appMobile=lulo'
				. '&app_key='. $incomCRM_config['app_mobile_key']
				. '&appVersion='. $incomCRM_config['app_customer_version']
				;
				if( $type == '3' || $type == 'web' ) $url .= '&appCustomer=1';
				break;
			
			case '1':
			default:
				$url = 'index.php?module=Users&action=Login';
				break;
		}
		return $url;
	}
	
}
?>