<?php
/*********************************************************************************
 * License Fix File
 * Created to override license checking functionality
 ********************************************************************************/

if(!defined('incomCRMEntry') || !incomCRMEntry) die('Not A Valid Entry Point');

/**
 * Override function to always return valid license
 * This prevents license violation errors
 */
if (!function_exists('_l_l_v5')) {
    function _l_l_v5() {
        // Always return valid license status
        global $license;

        // Clear any existing license error messages
        if (isset($_SESSION['HomeOnly'])) {
            unset($_SESSION['HomeOnly']);
        }

        // Clear license error globals
        if (isset($GLOBALS['login_error'])) {
            unset($GLOBALS['login_error']);
        }

        // Set valid license settings if license object exists
        if (isset($license) && is_object($license)) {
            // Clear any license error messages
            if (isset($license->settings['license_msg_all'])) {
                $license->settings['license_msg_all'] = '';
            }
            if (isset($license->settings['license_msg_admin'])) {
                $license->settings['license_msg_admin'] = '';
            }

            // Set valid license status
            $license->settings['license_validation'] = 'valid';
            $license->settings['license_status'] = 'active';
        }

        return true;
    }
}

/**
 * Override license checking functions
 */
if (!function_exists('authUserStatus')) {
    function authUserStatus() {
        // Always return valid user status
        return true;
    }
}

/**
 * Override loginLicense function to always pass
 */
if (!function_exists('loginLicense_override')) {
    function loginLicense_override() {
        global $current_user, $license, $authLevel;

        // Load license but don't perform validation
        if(empty($license)) {
            loadLicense();
        }

        // Set auth level to valid
        $authLevel = 1;

        // Clear any license-related session errors
        unset($_SESSION['license_seats_needed']);
        unset($_SESSION['COULD_NOT_CONNECT']);
        unset($_SESSION['EXCEEDING_OC_LICENSES']);

        // Always return success
        return true;
    }
}

?>
